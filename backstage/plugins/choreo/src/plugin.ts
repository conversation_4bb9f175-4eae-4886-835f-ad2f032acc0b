import {
  createPlugin,
  createRoutableExtension,
} from '@backstage/core-plugin-api';

import { rootRouteRef } from './routes';

export const choreoPlugin = createPlugin({
  id: 'choreo',
  routes: {
    root: rootRouteRef,
  },
});

export const ChoreoPage = choreoPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoPage',
    component: () =>
      import('./components/SignInComponent').then(m => m.SignInComponent),
    mountPoint: rootRouteRef,
  }),
);
