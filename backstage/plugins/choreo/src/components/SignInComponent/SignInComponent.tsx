import { useCallback, useMemo, useState, createElement } from 'react';
import {
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  makeStyles,
  Theme,
} from '@material-ui/core';
import { Page, Content } from '@backstage/core-components';
import { useApi, identityApiRef } from '@backstage/core-plugin-api';
import { useNavigate } from 'react-router-dom';
import BusinessIcon from '@material-ui/icons/Business';
import GitHubIcon from '@material-ui/icons/GitHub';
import EmailIcon from '@material-ui/icons/Email';

const useStyles = makeStyles((theme: Theme) => ({
  root: {
    minHeight: '100vh',
    backgroundColor: theme.palette.background.default,
  },
  container: {
    height: '100vh',
    padding: theme.spacing(4),
  },
  signInCard: {
    padding: theme.spacing(3),
    height: 'fit-content',
  },
  signInTitle: {
    marginBottom: theme.spacing(3),
    textAlign: 'center',
  },
  signInButton: {
    width: '100%',
    marginBottom: theme.spacing(2),
    justifyContent: 'flex-start',
    textTransform: 'none',
  },
  errorText: {
    marginTop: theme.spacing(2),
  },
}));

export interface SignInOption {
  icon: React.ComponentType;
  label: string;
  provider: string;
}

export interface SignInComponentProps {
  onSignIn?: (provider: string) => Promise<void> | void;
  redirectUrl?: string;
  availableProviders?: SignInOption[];
}

const defaultSignInOptions: SignInOption[] = [
  { icon: BusinessIcon, label: 'Continue with Google', provider: 'google' },
  { icon: GitHubIcon, label: 'Continue with GitHub', provider: 'github' },
  { icon: BusinessIcon, label: 'Continue with Microsoft', provider: 'microsoft' },
  { icon: BusinessIcon, label: 'Sign in with Enterprise ID', provider: 'enterprise' },
  { icon: EmailIcon, label: 'Sign in with Email', provider: 'email' },
];

export const SignInComponent = ({
  onSignIn,
  redirectUrl = '/choreo/home',
  availableProviders,
}: SignInComponentProps) => {
  const classes = useStyles();
  const navigate = useNavigate();
  const identityApi = useApi(identityApiRef);

  const options = useMemo(
    () => availableProviders ?? defaultSignInOptions,
    [availableProviders],
  );

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | undefined>(undefined);

  const handleSignIn = useCallback(
    async (provider: string): Promise<void> => {
      setLoading(true);
      setError(undefined);
      try {
        // Allow caller to implement provider-specific auth if provided
        if (onSignIn) {
          await onSignIn(provider);
        }

        // Try to touch identity; this helps surface auth issues early
        try {
          await identityApi.getProfileInfo();
        } catch {
          // Swallow errors here; actual auth flow may happen outside this component
        }

        // Navigate after successful (or delegated) sign-in handling
        navigate(redirectUrl);
      } catch (e) {
        setError(
          e instanceof Error ? e.message : 'Failed to sign in. Please try again.',
        );
      } finally {
        setLoading(false);
      }
    },
    [identityApi, navigate, onSignIn, redirectUrl],
  );

  return (
    <Page themeId="home" className={classes.root}>
      <Content className={classes.container}>
        <Grid
          container
          spacing={4}
          style={{ height: '100%' }}
          alignItems="center"
          justifyContent="center"
        >
          <Grid item xs={12} md={4}>
            <Card className={classes.signInCard}>
              <CardContent>
                <Typography variant="h5" className={classes.signInTitle}>
                  Sign In
                </Typography>

                {options.map((option, index) => (
                  <Button
                    key={index}
                    variant="outlined"
                    className={classes.signInButton}
                    startIcon={createElement(option.icon)}
                    onClick={() => handleSignIn(option.provider)}
                    aria-label={`Sign in with ${option.label}`}
                    aria-busy={loading ? 'true' : 'false'}
                    disabled={loading}
                    data-testid={`signin-btn-${option.provider}`}
                  >
                    {option.label}
                  </Button>
                ))}

                {error && (
                  <Typography
                    variant="body2"
                    color="error"
                    className={classes.errorText}
                    role="alert"
                    aria-live="polite"
                    data-testid="signin-error"
                  >
                    {error}
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Content>
    </Page>
  );
};