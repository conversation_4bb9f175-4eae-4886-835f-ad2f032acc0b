import { SignInComponent, SignInComponentProps, SignInOption } from './SignInComponent';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import {
  registerMswTestHooks,
  renderInTestApp,
  TestApiProvider,
} from '@backstage/test-utils';
import { identityApiRef } from '@backstage/core-plugin-api';
import BusinessIcon from '@material-ui/icons/Business';
import GitHubIcon from '@material-ui/icons/GitHub';

// Mock the useNavigate hook
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock identity API
const mockIdentityApi = {
  getProfileInfo: jest.fn(),
  getBackstageIdentity: jest.fn(),
  getCredentials: jest.fn(),
  signOut: jest.fn(),
};

describe('SignInComponent', () => {
  const server = setupServer();
  registerMswTestHooks(server);

  beforeEach(() => {
    jest.clearAllMocks();
    mockIdentityApi.getProfileInfo.mockResolvedValue({
      email: '<EMAIL>',
      displayName: 'Test User',
    });

    server.use(
      rest.get('/*', (_, res, ctx) => res(ctx.status(200), ctx.json({}))),
    );
  });

  const renderComponent = (props: Partial<SignInComponentProps> = {}) => {
    return renderInTestApp(
      <TestApiProvider apis={[[identityApiRef, mockIdentityApi]]}>
        <SignInComponent {...props} />
      </TestApiProvider>,
    );
  };

  describe('Basic Rendering', () => {
    it('should render the sign-in title', async () => {
      await renderComponent();
      expect(screen.getByText('Sign In')).toBeInTheDocument();
    });

    it('should render all default sign-in options', async () => {
      await renderComponent();

      expect(screen.getByTestId('signin-btn-google')).toBeInTheDocument();
      expect(screen.getByTestId('signin-btn-github')).toBeInTheDocument();
      expect(screen.getByTestId('signin-btn-microsoft')).toBeInTheDocument();
      expect(screen.getByTestId('signin-btn-enterprise')).toBeInTheDocument();
      expect(screen.getByTestId('signin-btn-email')).toBeInTheDocument();
    });

    it('should render custom providers when provided', async () => {
      const customProviders: SignInOption[] = [
        { icon: BusinessIcon, label: 'Custom Provider', provider: 'custom' },
        { icon: GitHubIcon, label: 'Another Provider', provider: 'another' },
      ];

      await renderComponent({ availableProviders: customProviders });

      expect(screen.getByTestId('signin-btn-custom')).toBeInTheDocument();
      expect(screen.getByTestId('signin-btn-another')).toBeInTheDocument();
      expect(screen.queryByTestId('signin-btn-google')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper aria-labels for sign-in buttons', async () => {
      await renderComponent();

      expect(screen.getByLabelText('Sign in with Continue with Google')).toBeInTheDocument();
      expect(screen.getByLabelText('Sign in with Continue with GitHub')).toBeInTheDocument();
    });

    it('should show aria-busy when loading', async () => {
      const slowOnSignIn = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
      await renderComponent({ onSignIn: slowOnSignIn });

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      expect(button).toHaveAttribute('aria-busy', 'true');
      expect(button).toBeDisabled();
    });

    it('should announce errors with proper ARIA attributes', async () => {
      const failingOnSignIn = jest.fn().mockRejectedValue(new Error('Auth failed'));
      await renderComponent({ onSignIn: failingOnSignIn });

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      await waitFor(() => {
        const errorElement = screen.getByTestId('signin-error');
        expect(errorElement).toHaveAttribute('role', 'alert');
        expect(errorElement).toHaveAttribute('aria-live', 'polite');
        expect(errorElement).toHaveTextContent('Auth failed');
      });
    });
  });

  describe('Navigation', () => {
    it('should navigate to default URL after successful sign-in', async () => {
      await renderComponent();

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/choreo/home');
      });
    });

    it('should navigate to custom redirect URL when provided', async () => {
      await renderComponent({ redirectUrl: '/custom/path' });

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/custom/path');
      });
    });
  });

  describe('Authentication Integration', () => {
    it('should call custom onSignIn handler when provided', async () => {
      const mockOnSignIn = jest.fn().mockResolvedValue(undefined);
      await renderComponent({ onSignIn: mockOnSignIn });

      const button = screen.getByTestId('signin-btn-github');
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockOnSignIn).toHaveBeenCalledWith('github');
      });
    });

    it('should call identity API to verify authentication', async () => {
      await renderComponent();

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockIdentityApi.getProfileInfo).toHaveBeenCalled();
      });
    });

    it('should handle identity API errors gracefully', async () => {
      mockIdentityApi.getProfileInfo.mockRejectedValue(new Error('Identity error'));
      await renderComponent();

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      // Should still navigate even if identity check fails
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/choreo/home');
      });
    });
  });

  describe('Error Handling', () => {
    it('should display error message when sign-in fails', async () => {
      const mockOnSignIn = jest.fn().mockRejectedValue(new Error('Authentication failed'));
      await renderComponent({ onSignIn: mockOnSignIn });

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByTestId('signin-error')).toHaveTextContent('Authentication failed');
      });
    });

    it('should display generic error message for non-Error objects', async () => {
      const mockOnSignIn = jest.fn().mockRejectedValue('String error');
      await renderComponent({ onSignIn: mockOnSignIn });

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByTestId('signin-error')).toHaveTextContent('Failed to sign in. Please try again.');
      });
    });

    it('should clear error when attempting new sign-in', async () => {
      const mockOnSignIn = jest.fn()
        .mockRejectedValueOnce(new Error('First error'))
        .mockResolvedValueOnce(undefined);

      await renderComponent({ onSignIn: mockOnSignIn });

      const button = screen.getByTestId('signin-btn-google');

      // First click - should show error
      fireEvent.click(button);
      await waitFor(() => {
        expect(screen.getByTestId('signin-error')).toBeInTheDocument();
      });

      // Second click - should clear error
      fireEvent.click(button);
      await waitFor(() => {
        expect(screen.queryByTestId('signin-error')).not.toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading state during sign-in process', async () => {
      let resolveSignIn: () => void;
      const mockOnSignIn = jest.fn(() => new Promise<void>(resolve => {
        resolveSignIn = resolve;
      }));

      await renderComponent({ onSignIn: mockOnSignIn });

      const button = screen.getByTestId('signin-btn-google');
      fireEvent.click(button);

      // Should be disabled and show loading
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('aria-busy', 'true');

      // Resolve the promise
      resolveSignIn!();

      await waitFor(() => {
        expect(button).not.toBeDisabled();
        expect(button).toHaveAttribute('aria-busy', 'false');
      });
    });

    it('should disable all buttons during loading', async () => {
      let resolveSignIn: () => void;
      const mockOnSignIn = jest.fn(() => new Promise<void>(resolve => {
        resolveSignIn = resolve;
      }));

      await renderComponent({ onSignIn: mockOnSignIn });

      const googleButton = screen.getByTestId('signin-btn-google');
      const githubButton = screen.getByTestId('signin-btn-github');

      fireEvent.click(googleButton);

      // All buttons should be disabled
      expect(googleButton).toBeDisabled();
      expect(githubButton).toBeDisabled();

      resolveSignIn!();

      await waitFor(() => {
        expect(googleButton).not.toBeDisabled();
        expect(githubButton).not.toBeDisabled();
      });
    });
  });

  describe('User Interactions', () => {
    it('should call onSignIn with correct provider for Google', async () => {
      const mockOnSignIn = jest.fn().mockResolvedValue(undefined);
      await renderComponent({ onSignIn: mockOnSignIn });

      fireEvent.click(screen.getByTestId('signin-btn-google'));
      await waitFor(() => expect(mockOnSignIn).toHaveBeenCalledWith('google'));
      expect(mockOnSignIn).toHaveBeenCalledTimes(1);
    });

    it('should call onSignIn with correct provider for GitHub', async () => {
      const mockOnSignIn = jest.fn().mockResolvedValue(undefined);
      await renderComponent({ onSignIn: mockOnSignIn });

      fireEvent.click(screen.getByTestId('signin-btn-github'));
      await waitFor(() => expect(mockOnSignIn).toHaveBeenCalledWith('github'));
      expect(mockOnSignIn).toHaveBeenCalledTimes(1);
    });

    it('should prevent multiple simultaneous sign-in attempts', async () => {
      let resolveSignIn: () => void;
      const mockOnSignIn = jest.fn(() => new Promise<void>(resolve => {
        resolveSignIn = resolve;
      }));

      await renderComponent({ onSignIn: mockOnSignIn });

      const button = screen.getByTestId('signin-btn-google');

      // Click multiple times rapidly
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      // Should only be called once
      expect(mockOnSignIn).toHaveBeenCalledTimes(1);

      resolveSignIn!();
      await waitFor(() => expect(button).not.toBeDisabled());
    });
  });
});